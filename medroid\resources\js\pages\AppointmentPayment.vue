<script setup>
import { ref, onMounted } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import axios from 'axios'

const props = defineProps({
    appointment: Object,
    stripe_public_key: String
})

const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// Card form data
const cardForm = ref({
    card_number: '',
    exp_month: '',
    exp_year: '',
    cvc: '',
    cardholder_name: ''
})

const breadcrumbs = [
    { name: 'Appointments', href: '/appointments' },
    { name: 'Payment', href: '#' }
]

const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    })
}

const formatTime = (timeSlot) => {
    if (typeof timeSlot === 'object' && timeSlot.start_time) {
        return `${timeSlot.start_time} - ${timeSlot.end_time}`
    }
    return timeSlot
}

const processPayment = async () => {
    if (!validateForm()) {
        return
    }

    loading.value = true
    errorMessage.value = ''
    successMessage.value = ''

    try {
        // Check if payment_intent_id exists
        if (!props.appointment.payment_intent_id) {
            errorMessage.value = 'Payment intent not found. Please refresh the page and try again.'
            loading.value = false
            return
        }

        const paymentData = {
            payment_intent_id: props.appointment.payment_intent_id,
            appointment_id: props.appointment.id,
            card_number: cardForm.value.card_number.replace(/\s/g, ''),
            exp_month: parseInt(cardForm.value.exp_month),
            exp_year: parseInt(cardForm.value.exp_year),
            cvc: cardForm.value.cvc,
            cardholder_name: cardForm.value.cardholder_name
        }

        console.log('Payment data:', paymentData) // Debug log

        const response = await axios.post('/process-web-payment', paymentData)

        if (response.data.success) {
            successMessage.value = 'Payment processed successfully! Redirecting...'
            
            // Redirect to appointments page after success
            setTimeout(() => {
                router.visit('/appointments', {
                    onSuccess: () => {
                        // Show success message on appointments page
                        router.reload({
                            data: { message: 'Payment completed successfully!' }
                        })
                    }
                })
            }, 2000)
        } else {
            errorMessage.value = response.data.message || 'Payment failed. Please try again.'
        }
    } catch (error) {
        console.error('Payment error:', error)
        if (error.response?.data?.message) {
            errorMessage.value = error.response.data.message
        } else if (error.response?.data?.errors) {
            const errors = Object.values(error.response.data.errors).flat()
            errorMessage.value = errors.join(', ')
        } else {
            errorMessage.value = 'An error occurred while processing your payment. Please try again.'
        }
    } finally {
        loading.value = false
    }
}

const validateForm = () => {
    if (!cardForm.value.card_number || !cardForm.value.exp_month || !cardForm.value.exp_year || 
        !cardForm.value.cvc || !cardForm.value.cardholder_name) {
        errorMessage.value = 'Please fill in all required fields.'
        return false
    }

    // Basic card number validation (remove spaces and check length)
    const cardNumber = cardForm.value.card_number.replace(/\s/g, '')
    if (cardNumber.length < 13 || cardNumber.length > 19) {
        errorMessage.value = 'Please enter a valid card number.'
        return false
    }

    // Expiry validation
    const currentYear = new Date().getFullYear()
    const currentMonth = new Date().getMonth() + 1
    const expYear = parseInt(cardForm.value.exp_year)
    const expMonth = parseInt(cardForm.value.exp_month)

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
        errorMessage.value = 'Card has expired. Please use a valid card.'
        return false
    }

    // CVC validation
    if (cardForm.value.cvc.length < 3 || cardForm.value.cvc.length > 4) {
        errorMessage.value = 'Please enter a valid CVC.'
        return false
    }

    return true
}

// Format card number with spaces
const formatCardNumber = () => {
    let value = cardForm.value.card_number.replace(/\s/g, '').replace(/[^0-9]/gi, '')
    const formattedValue = value.match(/.{1,4}/g)?.join(' ') || value
    cardForm.value.card_number = formattedValue
}

const goBack = () => {
    router.visit('/appointments')
}
</script>

<template>
    <Head title="Payment - Medroid" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-w-2xl mx-auto py-8 px-4">
            <!-- Appointment Details -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-4">Complete Payment</h1>
                
                <div class="border-l-4 border-blue-500 pl-4 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-2">Appointment Details</h2>
                    <div class="space-y-2 text-sm text-gray-600">
                        <p><span class="font-medium">Provider:</span> {{ appointment.provider.name }}</p>
                        <p><span class="font-medium">Specialization:</span> {{ appointment.provider.specialization }}</p>
                        <p><span class="font-medium">Service:</span> {{ appointment.service.name }}</p>
                        <p><span class="font-medium">Date:</span> {{ formatDate(appointment.date) }}</p>
                        <p><span class="font-medium">Time:</span> {{ formatTime(appointment.time_slot) }}</p>
                        <p><span class="font-medium">Reason:</span> {{ appointment.reason }}</p>
                        <p v-if="appointment.notes"><span class="font-medium">Notes:</span> {{ appointment.notes }}</p>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                        <span class="text-2xl font-bold text-green-600">£{{ appointment.amount }}</span>
                    </div>
                    <!-- Debug info - remove in production -->
                    <div v-if="appointment.payment_intent_id" class="mt-2 text-xs text-gray-500">
                        Payment Intent: {{ appointment.payment_intent_id }}
                    </div>
                    <div v-else class="mt-2 text-xs text-red-500">
                        No payment intent found
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

                <!-- Error Message -->
                <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-800">{{ errorMessage }}</p>
                        </div>
                    </div>
                </div>

                <!-- Success Message -->
                <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-green-800">{{ successMessage }}</p>
                        </div>
                    </div>
                </div>

                <form @submit.prevent="processPayment" class="space-y-4">
                    <!-- Cardholder Name -->
                    <div>
                        <label for="cardholder_name" class="block text-sm font-medium text-gray-700 mb-1">
                            Cardholder Name *
                        </label>
                        <input
                            id="cardholder_name"
                            v-model="cardForm.cardholder_name"
                            type="text"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="John Doe"
                        />
                    </div>

                    <!-- Card Number -->
                    <div>
                        <label for="card_number" class="block text-sm font-medium text-gray-700 mb-1">
                            Card Number *
                        </label>
                        <input
                            id="card_number"
                            v-model="cardForm.card_number"
                            @input="formatCardNumber"
                            type="text"
                            required
                            maxlength="23"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="1234 5678 9012 3456"
                        />
                    </div>

                    <!-- Expiry and CVC -->
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label for="exp_month" class="block text-sm font-medium text-gray-700 mb-1">
                                Month *
                            </label>
                            <select
                                id="exp_month"
                                v-model="cardForm.exp_month"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">MM</option>
                                <option v-for="month in 12" :key="month" :value="month.toString().padStart(2, '0')">
                                    {{ month.toString().padStart(2, '0') }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="exp_year" class="block text-sm font-medium text-gray-700 mb-1">
                                Year *
                            </label>
                            <select
                                id="exp_year"
                                v-model="cardForm.exp_year"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">YYYY</option>
                                <option v-for="year in 20" :key="year" :value="(new Date().getFullYear() + year - 1).toString()">
                                    {{ new Date().getFullYear() + year - 1 }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="cvc" class="block text-sm font-medium text-gray-700 mb-1">
                                CVC *
                            </label>
                            <input
                                id="cvc"
                                v-model="cardForm.cvc"
                                type="text"
                                required
                                maxlength="4"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="123"
                            />
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-4 pt-6">
                        <button
                            type="button"
                            @click="goBack"
                            class="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            :disabled="loading"
                            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span v-if="loading" class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                            <span v-else>
                                Pay £{{ appointment.amount }}
                            </span>
                        </button>
                    </div>
                </form>

                <!-- Security Notice -->
                <div class="mt-6 p-4 bg-gray-50 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-gray-600">
                                Your payment information is secure and encrypted. We use Stripe for payment processing.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
