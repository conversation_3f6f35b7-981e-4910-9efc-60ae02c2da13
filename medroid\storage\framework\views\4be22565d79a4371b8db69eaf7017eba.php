

<?php $__env->startSection('content'); ?>
    <h1>Your Appointment Has Been Booked</h1>
    
    <p>Hello <?php echo e($patient->name); ?>,</p>
    
    <p>Your appointment with Dr. <?php echo e($provider->name); ?> has been successfully booked.</p>
    
    <div class="appointment-details">
        <p><strong>Date:</strong> <?php echo e($date); ?></p>
        <p><strong>Time:</strong> <?php echo e($startTime); ?> - <?php echo e($endTime); ?></p>
        <p><strong>Provider:</strong> Dr. <?php echo e($provider->name); ?></p>
        <p><strong>Reason:</strong> <?php echo e($appointment->reason); ?></p>
        <?php if($appointment->is_telemedicine): ?>
            <p><strong>Type:</strong> Telemedicine (Video Consultation)</p>
        <?php else: ?>
            <p><strong>Type:</strong> In-person Visit</p>
        <?php endif; ?>
        <p><strong>Status:</strong> <?php echo e(ucfirst($appointmentStatus)); ?></p>
        <p><strong>Payment Status:</strong> <?php echo e(ucfirst($paymentStatus)); ?></p>
    </div>
    
    <?php if($appointmentStatus == 'pending_payment'): ?>
        <p>Please complete your payment to confirm this appointment. You can do this through the Medroid app.</p>
        
        <p style="text-align: center; margin: 25px 0;">
            <a href="<?php echo e(config('app.url')); ?>/app/appointments/<?php echo e($appointment->id); ?>/payment" class="btn">Complete Payment</a>
        </p>
    <?php endif; ?>
    
    <p>You can view and manage your appointments in the Medroid app.</p>
    
    <?php if($appointment->is_telemedicine): ?>
        <p>For telemedicine appointments, you'll receive a link to join the video consultation 15 minutes before your scheduled time.</p>
    <?php endif; ?>
    
    <p>If you need to reschedule or cancel this appointment, please do so at least 24 hours in advance.</p>
    
    <p>Thank you for choosing Medroid Health Care for your healthcare needs.</p>
    
    <p>Best regards,<br>
    The Medroid Health Care Team</p>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('emails.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\medroid-app\medroid-full\medroid\resources\views/emails/appointment-booked-patient.blade.php ENDPATH**/ ?>